import os
import sys
import asyncio
import uvicorn
from fastapi import FastAPI, Query
from fastapi.middleware.cors import CORSMiddleware
import logging

# 从环境变量获取日志级别，默认为INFO
log_level = os.getenv('LOG_LEVEL', 'INFO').upper()

# 配置日志
logging.basicConfig(
    level=logging.getLevelName(log_level),
    format='[%(asctime)s][%(levelname)s][%(filename)s:%(lineno)d] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'commons'))
import database
import token_price
import kline_service

app = FastAPI()

@app.get("/alphafi/agent/query/token/price", summary="查询代币价格信息接口", description="查询price_change_percentage_24h、name、current_price、ath、atl、create_time信息，可选择按symbol过滤")
async def query_token_price(
    symbol: str = Query(None, description="代币符号，可选"),
    sort_column: str = Query("price_change_percentage_24h", description="排序字段，可选值: price_change_percentage_24h, current_price, ath, atl"),
    sort_order: str = Query("desc", description="排序顺序，可选值: asc(升序), desc(降序)"),
    limit: int = Query(5, description="返回数据数量限制", ge=1)
):
    """查询代币价格信息接口

    Args:
        symbol (str, optional): 代币符号，默认为None
        sort_column (str): 排序字段，默认为 'price_change_percentage_24h'
        sort_order (str): 排序顺序，默认为 'desc'
        limit (int): 返回数据数量限制，默认为 10

    Returns:
        dict: 包含代币价格信息的响应
    """
    # 验证排序字段
    valid_sort_fields = ['price_change_percentage_24h', 'current_price', 'ath', 'atl']
    if sort_column not in valid_sort_fields:
        sort_column = 'price_change_percentage_24h'

    # 验证排序顺序
    valid_sort_orders = ['asc', 'desc']
    if sort_order.lower() not in valid_sort_orders:
        sort_order = 'desc'

    # 调用服务层获取数据
    result = await token_price.TokenPriceService.query_token_price(
        symbol=symbol,
        sort_column=sort_column,
        sort_order=sort_order,
        limit=limit
    )

    return {
        "code": 200,
        "message": "success",
        "data": result
    }


@app.get("/public/v1/kline/{symbol}", summary="查询K线数据接口", description="查询指定交易对的K线数据")
async def query_kline_data(
    symbol: str,
    interval: str = Query(..., description="K线间隔 (1d, 1h, 1w, 1M)"),
    startTime: int = Query(None, description="开始时间戳(秒)"),
    endTime: int = Query(None, description="结束时间戳(秒)"),
    limit: int = Query(500, description="返回数据数量限制", ge=1, le=1000)
):
    """查询K线数据接口

    Args:
        symbol (str): 交易对符号
        interval (str): K线间隔 (1d, 1h, 1w, 1M)
        startTime (int, optional): 开始时间戳(秒)
        endTime (int, optional): 结束时间戳(秒)
        limit (int): 返回数据数量限制，默认为500

    Returns:
        dict: 包含K线数据的响应
    """
    try:
        # 调用服务层获取数据
        result = await kline_service.KLineService.query_kline_data(
            symbol=symbol,
            interval=interval,
            start_time=startTime,
            end_time=endTime,
            limit=limit
        )

        return {
            "code": 200,
            "message": "success",
            "data": result
        }
    except ValueError as e:
        return {
            "code": 400,
            "message": str(e),
            "data": []
        }
    except Exception as e:
        logger.error(f"Error in query_kline_data: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {
            "code": 500,
            "message": f"Internal server error: {str(e)}",
            "data": []
        }


async def init():
    # 初始化数据库连接
    await database.initialize_database()

if __name__ == "__main__":
    asyncio.run(init())
    app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )

    port = int(os.getenv('SERVER_PORT', 8000))
    logger.info(f"Starting server on port {port}")
    uvicorn.run(
        app,
        host=os.getenv('SERVER_HOST', "0.0.0.0"),
        port=port,
        log_config={
            "version": 1,
            "formatters": {
                "default": {
                    "()": "uvicorn.logging.DefaultFormatter",
                    "fmt": "[%(asctime)s][%(levelname)s][%(name)s][%(filename)s:%(lineno)d] %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                }
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr"
                }
            },
            "loggers": {
                "uvicorn": {"handlers": ["default"], "level": log_level}
            }
        }
    )