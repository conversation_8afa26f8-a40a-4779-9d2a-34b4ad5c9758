package services

import (
	"context"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

// 我们将直接在代码中定义Lua脚本，避免embed路径问题
var (
	releaseResourceScript = `
-- 释放全局限流资源的Lua脚本
local key = KEYS[1]
local reqid = ARGV[1]

-- 参数验证
if not key or not reqid then
    return {0, 0, "Invalid parameters"}
end

-- 检查reqid是否存在
local existing_score = redis.call('ZSCORE', key, reqid)
if not existing_score then
    -- reqid不存在，可能已经被清理或从未添加
    local current_count = redis.call('ZCARD', key)
    return {1, current_count, "ReqID not found (already released or expired)"}
end

-- 移除reqid
local removed_count = redis.call('ZREM', key, reqid)
if removed_count == 0 then
    -- 移除失败（理论上不应该发生，因为上面已经检查过存在性）
    local current_count = redis.call('ZCARD', key)
    return {0, current_count, "Failed to remove reqid"}
end

-- 获取移除后的并发数
local remaining_count = redis.call('ZCARD', key)

-- 如果zset为空，删除key以节省内存
if remaining_count == 0 then
    redis.call('DEL', key)
end

return {1, remaining_count, "Resource released successfully"}
`

	// 组合限流脚本（令牌桶 + 并发限制）
	combinedRateLimitScript = `
-- 组合限流脚本：令牌桶QPS限制 + 并发限制
-- KEYS[1]: 并发限制key (格式: global_rate_limiter:{limiter_id})
-- KEYS[2]: 令牌桶key (格式: token_bucket:{limiter_id})
-- KEYS[3]: 上次补充时间key (格式: last_refill:{limiter_id})
-- ARGV[1]: reqid (请求id， 一般调用时随机生成)
-- ARGV[2]: max_concurrency (最大并发数)
-- ARGV[3]: call_timeout (并发者的处理超时时间，秒)
-- ARGV[4]: current_timestamp (当前时间戳，秒)
-- ARGV[5]: max_qps (最大QPS，支持小数，0表示不限制)
-- ARGV[6]: bucket_capacity (令牌桶容量)
--
-- 返回值: {allowed, current_concurrency, current_tokens, message}
-- allowed: 1表示允许，0表示不允许
-- current_concurrency: 当前并发数
-- current_tokens: 当前剩余令牌数
-- message: 描述信息

local concurrency_key = KEYS[1]
local bucket_key = KEYS[2]
local last_refill_key = KEYS[3]
local reqid = ARGV[1]
local max_concurrency = tonumber(ARGV[2])
local call_timeout = tonumber(ARGV[3])
local current_timestamp = tonumber(ARGV[4])
local max_qps = tonumber(ARGV[5])
local bucket_capacity = tonumber(ARGV[6])

-- 参数验证
if not reqid or not max_concurrency or not call_timeout or not current_timestamp or not max_qps then
    return {0, 0, 0, "Invalid parameters"}
end

-- 第一步：QPS限制检查（如果配置了QPS限制）
local current_tokens = bucket_capacity
if max_qps > 0 then
    local current_time_ms = current_timestamp * 1000 -- 转换为毫秒

    -- 获取当前令牌数和上次补充时间
    current_tokens = tonumber(redis.call('GET', bucket_key) or bucket_capacity)
    local last_refill = tonumber(redis.call('GET', last_refill_key))

    -- 如果是第一次调用（last_refill_key不存在），初始化时间
    if not last_refill then
        last_refill = current_time_ms
        redis.call('SET', last_refill_key, last_refill)
        redis.call('EXPIRE', last_refill_key, 3600)
    end

    -- 计算需要补充的令牌数（基于秒：1000ms = 1秒）
    local time_passed_ms = current_time_ms - last_refill
    local tokens_to_add = (time_passed_ms / 1000.0) * max_qps

    -- 记录补充前的令牌数
    local old_tokens = current_tokens

    -- 补充令牌（不超过桶容量）
    current_tokens = math.min(bucket_capacity, current_tokens + tokens_to_add)

    -- 计算实际补充的令牌数
    local actual_tokens_added = current_tokens - old_tokens

    -- 只有实际补充了令牌才更新时间
    if actual_tokens_added > 0 then
        redis.call('SET', last_refill_key, current_time_ms)
        redis.call('SET', bucket_key, current_tokens)
    end

	redis.call('EXPIRE', bucket_key, 3600)
	redis.call('EXPIRE', last_refill_key, 3600)

    -- 检查是否有可用令牌
    if current_tokens < 1 then
        -- 没有令牌且没有补充令牌，不更新时间，保持累积
        return {0, 0, math.floor(current_tokens * 100) / 100, "QPS limit exceeded"}
    end

    -- 预先消耗一个令牌（如果后续并发检查失败会回滚）
    current_tokens = current_tokens - 1
    redis.call('SET', bucket_key, current_tokens)
end

-- 第二步：并发限制检查
local expire_before = current_timestamp - call_timeout

-- 清理过期的reqid
redis.call('ZREMRANGEBYSCORE', concurrency_key, 0, expire_before)

-- 检查reqid是否已存在
local existing_score = redis.call('ZSCORE', concurrency_key, reqid)
if existing_score then
    local current_concurrency = redis.call('ZCARD', concurrency_key)
    return {1, current_concurrency, math.floor(current_tokens * 100) / 100, "ReqID already exists"}
end

-- 检查当前并发数
local current_concurrency = redis.call('ZCARD', concurrency_key)
if current_concurrency >= max_concurrency then
    -- 并发限制失败，需要回滚QPS令牌
    if max_qps > 0 then
        current_tokens = current_tokens + 1
        redis.call('SET', bucket_key, current_tokens)
    end
    return {0, current_concurrency, math.floor(current_tokens * 100) / 100, "Concurrency limit exceeded"}
end

-- 添加reqid到并发控制zset
redis.call('ZADD', concurrency_key, current_timestamp, reqid)
redis.call('EXPIRE', concurrency_key, call_timeout * 2)

local new_concurrency = redis.call('ZCARD', concurrency_key)
return {1, new_concurrency, math.floor(current_tokens * 100) / 100, "Resource acquired successfully"}
`
)

// RedisClientService Redis客户端服务
type RedisClientService struct {
	client                     *redis.Client
	releaseScriptSHA           string
	combinedRateLimitScriptSHA string
	releaseScript              string
	combinedRateLimitScript    string
}

// NewRedisClientService 创建Redis客户端服务
func NewRedisClientService() (*RedisClientService, error) {
	// 从环境变量获取Redis配置
	redisAddr := os.Getenv("REDIS_ADDR")
	if redisAddr == "" {
		redisAddr = "localhost:6379"
	}

	redisPassword := os.Getenv("REDIS_PASSWORD")

	redisDBStr := os.Getenv("REDIS_DB")
	redisDB := 0
	if redisDBStr != "" {
		if db, err := strconv.Atoi(redisDBStr); err == nil {
			redisDB = db
		}
	}

	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:         redisAddr,
		Password:     redisPassword,
		DB:           redisDB,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10,
		MinIdleConns: 5,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("Redis连接失败: %w", err)
	}

	service := &RedisClientService{
		client: rdb,
	}

	// 加载Lua脚本
	if err := service.loadLuaScripts(ctx); err != nil {
		return nil, fmt.Errorf("加载Lua脚本失败: %w", err)
	}

	log.Printf("Redis客户端服务初始化成功，连接地址: %s", redisAddr)
	return service, nil
}

// loadLuaScripts 加载Lua脚本到Redis
func (r *RedisClientService) loadLuaScripts(ctx context.Context) error {
	// 使用内联的脚本
	r.releaseScript = releaseResourceScript
	r.combinedRateLimitScript = combinedRateLimitScript

	// 预加载脚本到Redis
	releaseSHA, err := r.client.ScriptLoad(ctx, r.releaseScript).Result()
	if err != nil {
		return fmt.Errorf("加载release脚本失败: %w", err)
	}
	r.releaseScriptSHA = releaseSHA

	combinedSHA, err := r.client.ScriptLoad(ctx, r.combinedRateLimitScript).Result()
	if err != nil {
		return fmt.Errorf("加载combinedRateLimit脚本失败: %w", err)
	}
	r.combinedRateLimitScriptSHA = combinedSHA

	log.Printf("Lua脚本加载成功: release=%s, combined=%s",
		releaseSHA[:8], combinedSHA[:8])

	return nil
}

// AcquireResource 获取资源（支持并发+QPS双重限制）
func (r *RedisClientService) AcquireResource(ctx context.Context, limiterID, reqID string, maxConcurrency, callTimeout int, maxQPS float64) (bool, int, int, string, error) {
	concurrencyKey := fmt.Sprintf("global_rate_limiter:%s", limiterID)
	bucketKey := fmt.Sprintf("token_bucket:%s", limiterID)
	lastRefillKey := fmt.Sprintf("last_refill:%s", limiterID)
	currentTimestamp := time.Now().Unix()

	// 统一使用组合限流脚本，maxQPS为0时脚本内部会跳过QPS检查
	// 对于小数QPS，桶容量设为QPS值，但至少为1
	// bucketCapacity := maxQPS
	bucketCapacity := 1 // 桶容量目前都设为1
	if maxQPS < 1 {
		bucketCapacity = 1 // 小数QPS时，桶容量至少为1
	}

	result, err := r.client.EvalSha(ctx, r.combinedRateLimitScriptSHA,
		[]string{concurrencyKey, bucketKey, lastRefillKey},
		reqID, maxConcurrency, callTimeout, currentTimestamp, maxQPS, bucketCapacity).Result()
	if err != nil {
		if isScriptNotFoundError(err) {
			if loadErr := r.loadLuaScripts(ctx); loadErr != nil {
				return false, 0, 0, "", fmt.Errorf("重新加载脚本失败: %w", loadErr)
			}
			result, err = r.client.EvalSha(ctx, r.combinedRateLimitScriptSHA,
				[]string{concurrencyKey, bucketKey, lastRefillKey},
				reqID, maxConcurrency, callTimeout, currentTimestamp, maxQPS, bucketCapacity).Result()
		}
		if err != nil {
			return false, 0, 0, "", fmt.Errorf("执行combinedRateLimit脚本失败: %w", err)
		}
	}

	resultSlice, ok := result.([]interface{})
	if !ok || len(resultSlice) != 4 {
		return false, 0, 0, "", fmt.Errorf("脚本返回格式错误: %v", result)
	}

	allowed := resultSlice[0].(int64) == 1
	currentConcurrency := int(resultSlice[1].(int64))
	currentTokens := int(resultSlice[2].(int64))
	message := resultSlice[3].(string)

	return allowed, currentConcurrency, currentTokens, message, nil
}

// ReleaseResource 释放并发资源
func (r *RedisClientService) ReleaseResource(ctx context.Context, limiterID, reqID string) (bool, int, string, error) {
	key := fmt.Sprintf("global_rate_limiter:%s", limiterID)

	// 执行Lua脚本
	result, err := r.client.EvalSha(ctx, r.releaseScriptSHA, []string{key}, reqID).Result()
	if err != nil {
		// 如果脚本不存在，重新加载并执行
		if isScriptNotFoundError(err) {
			log.Printf("脚本不存在，重新加载: %v", err)
			if loadErr := r.loadLuaScripts(ctx); loadErr != nil {
				return false, 0, "", fmt.Errorf("重新加载脚本失败: %w", loadErr)
			}
			result, err = r.client.EvalSha(ctx, r.releaseScriptSHA, []string{key}, reqID).Result()
		}
		if err != nil {
			return false, 0, "", fmt.Errorf("执行release脚本失败: %w", err)
		}
	}

	// 解析结果
	resultSlice, ok := result.([]interface{})
	if !ok || len(resultSlice) != 3 {
		return false, 0, "", fmt.Errorf("脚本返回格式错误: %v", result)
	}

	success := resultSlice[0].(int64) == 1
	remainingCount := int(resultSlice[1].(int64))
	message := resultSlice[2].(string)

	return success, remainingCount, message, nil
}

// GetCurrentConcurrency 获取当前并发数
func (r *RedisClientService) GetCurrentConcurrency(ctx context.Context, limiterID string) (int, error) {
	key := fmt.Sprintf("global_rate_limiter:%s", limiterID)
	count, err := r.client.ZCard(ctx, key).Result()
	if err != nil {
		return 0, fmt.Errorf("获取并发数失败: %w", err)
	}
	return int(count), nil
}

// isScriptNotFoundError 检查是否是脚本不存在的错误
func isScriptNotFoundError(err error) bool {
	return err != nil && (err.Error() == "NOSCRIPT No matching script. Please use EVAL." ||
		err.Error() == "NOSCRIPT No matching script")
}

// Close 关闭Redis连接
func (r *RedisClientService) Close() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}
