version: 1

apps:
  - appID: biz_account
    appDirPath: ../
    appPort: 8100 # 8000起
    command: ["go", "run", "main.go"]
    resourcesPath: "./dapr/components/dev"

    env:
      SERVER_HOST: "0.0.0.0"
      SERVER_PORT: "8100"
      DAPR_SIDECAR_PORT: "3600"

      LOG_LEVEL: "debug"
      # JWT相关配置
      JWT_DAPR_SECRET_STORE: "secretstore"
      JWT_SECRET_KEY_NAME: "jwt-secret"
      JWT_EXPIRATION_HOURS: "720"
      JWT_ISSUER: "alphafi"

    # Dapr sidecar配置
    daprHTTPPort: 3600  # 3500起
    daprGRPCPort: 36000
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: error
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6190 # 6090起
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3