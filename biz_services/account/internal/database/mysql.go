package database

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	_ "github.com/go-sql-driver/mysql"
)

var db *sql.DB

// InitDB 初始化数据库连接
func InitDB() error {
	var (
		host     string
		port     int
		user     string
		password string
		database string
	)

	// 尝试从Dapr Secret Store获取数据库配置
	client, err := dapr.NewClient()
	if err != nil {
		log.Printf("Failed to create Dapr client: %v, using environment variables as fallback", err)
		host = getEnv("MYSQL_HOST", "localhost")
		port, _ = strconv.Atoi(getEnv("MYSQL_PORT", "3306"))
		user = getEnv("MYSQL_USER", "root")
		password = getEnv("MYSQL_PASSWORD", "123456")
		database = getEnv("MYSQL_DATABASE", "testdb")
	} else {
		// defer client.Close()

		// 从Dapr Secret Store获取数据库配置
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		secrets, err := client.GetBulkSecret(ctx, "secretstore", nil)

		if err != nil {
			log.Printf("Failed to get secrets from Dapr Secret Store: %v, using environment variables as fallback", err)
			host = getEnv("MYSQL_HOST", "localhost")
			port, _ = strconv.Atoi(getEnv("MYSQL_PORT", "3306"))
			user = getEnv("MYSQL_USER", "root")
			password = getEnv("MYSQL_PASSWORD", "123456")
			database = getEnv("MYSQL_DATABASE", "testdb")
		} else {
			// 从secrets中提取配置
			host = getSecretValue(secrets, "MYSQL_HOST", "localhost")
			portStr := getSecretValue(secrets, "MYSQL_PORT", "3306")
			port, _ = strconv.Atoi(portStr)
			user = getSecretValue(secrets, "MYSQL_USER", "root")
			password = getSecretValue(secrets, "MYSQL_PASSWORD", "123456")
			database = getSecretValue(secrets, "MYSQL_DATABASE", "testdb")
		}
	}

	// 构建数据源名称
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true&loc=Local", user, password, host, port, database)

	var dbErr error
	db, dbErr = sql.Open("mysql", dsn)
	if dbErr != nil {
		return fmt.Errorf("failed to open database: %v", dbErr)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(25)
	db.SetConnMaxLifetime(5 * time.Minute)

	// 测试连接
	if err := db.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}

	log.Printf("Database connection established - Host: %s, Port: %d, User: %s, Database: %s", host, port, user, database)
	return nil
}

// GetDB 获取数据库连接实例
func GetDB() *sql.DB {
	return db
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getSecretValue 从Dapr secrets中获取值，如果不存在则返回默认值
func getSecretValue(secrets map[string]map[string]string, key, defaultValue string) string {
	if secretMap, ok := secrets[key]; ok {
		if value, ok := secretMap[key]; ok {
			return value
		}
	}
	// 回退到环境变量
	return getEnv(key, defaultValue)
}
