# Makefile for Crawler Task Manager Service

.PHONY: build clean deps run stop

deps:
	go mod tidy

build: deps
	go build -o bin/task-manager cmd/task_mgr/main.go
	go build -o bin/task-scheduler cmd/task_scheduler/main.go
	go build -o bin/task-executor cmd/task_executor/main.go
	go build -o bin/spider-manager cmd/spider_manager/main.go
	go build -o bin/spider cmd/spider/main.go
	@echo "Build complete"

clean:
	rm -rf bin/
	go clean

run: build
	dapr run -f dapr/run-prod.yaml

run-dev: deps
	dapr run -f dapr/run-dev.yaml

# 停止系统所有Dapr应用
stop:
	dapr stop task-manager
	dapr stop task-scheduler
	dapr stop task-executor
	dapr stop spider-manager
	dapr stop JSON_API-Spider
	dapr stop File-Spider
	@echo "所有Dapr应用已停止"

##########################################################
# 运行任务管理服务（命令方式）
# 变量定义
APP_NAME=task-manager
APP_PORT=8080
DAPR_HTTP_PORT=3500
DAPR_GRPC_PORT=50000
dapr-run-cmd:
	dapr run \
		--app-id $(APP_NAME) \
		--app-port $(APP_PORT) \
		--dapr-http-port $(DAPR_HTTP_PORT) \
		--dapr-grpc-port $(DAPR_GRPC_PORT) \
		--components-path ./dapr/components \
		--config ./dapr/config.yaml \
		-- go run cmd/task_mgr/main.go

# 运行任务管理服务（配置方式）
dapr-run-config:
	dapr run -f dapr/run-task-manager.yaml

# 停止任务管理服务
dapr-stop:
	dapr stop $(APP_NAME)

##########################################################
# 启动Redis（用于开发）
redis-start:
	docker run -d --name redis-crawler -p 6379:6379 redis:latest

# 停止Redis
redis-stop:
	docker stop redis-crawler
	docker rm redis-crawler

# 启动MySQL（用于开发）
mysql-start:
	docker-compose -f docker/docker-compose-mysql.yml up -d

# 停止MySQL
mysql-stop:
	docker-compose -f docker/docker-compose-mysql.yml down

# 重启MySQL
mysql-restart:
	docker-compose -f docker/docker-compose-mysql.yml restart

# 查看MySQL日志
mysql-logs:
	docker-compose -f docker/docker-compose-mysql.yml logs -f mysql

# 连接到MySQL
mysql-connect:
	docker exec -it mysql-crawler mysql -u root -ppassword crawler_db

# 启动MongoDB
mongodb-start:
	docker run -d --name mongodb-crawler -p 27017:27017 mongo:latest

# 停止MongoDB
mongodb-stop:
	docker stop mongodb-crawler
	docker rm mongodb-crawler

# 设置MongoDB配置到Redis
setup-mongo-config:
	chmod +x scripts/mongo-setup.sh
	./scripts/mongo-setup.sh

##########################################################
# 开发环境设置（Redis）
dev-setup-redis: deps redis-start mongodb-start setup-mongo-config
	@echo "开发环境设置完成（使用Redis）"

# 开发环境设置（MySQL）
dev-setup-mysql: deps mysql-start mongodb-start setup-mongo-config
	@echo "开发环境设置完成（使用MySQL）"

# 切换到Redis状态存储
switch-to-redis:
	cp dapr/components/statestore-redis.yaml dapr/components/statestore.yaml
	@echo "已切换到Redis状态存储"

# 切换到MySQL状态存储
switch-to-mysql:
	cp dapr/components/statestore-mysql.yaml dapr/components/statestore.yaml
	@echo "已切换到MySQL状态存储"

##########################################################
# 监控相关命令

# 启动监控系统
monitor-start:
	@echo "启动监控系统..."
	docker-compose -f docker/docker-compose-monitoring.yml up -d

# 停止监控系统
monitor-stop:
	@echo "停止监控系统..."
	docker-compose -f docker/docker-compose-monitoring.yml down

##########################################################
# 构建Docker镜像
docker-build:
	docker build -t $(APP_NAME):latest ./docker

# 运行Docker容器
docker-run:
	docker run -p $(APP_PORT):$(APP_PORT) $(APP_NAME):latest

# 构建Spider Docker镜像
docker-build-spider:
	docker build -t spider:latest -f docker/Dockerfile.spider ./

# 运行Spider Docker容器
docker-run-spider:
	docker run -p 8083:8083 spider:latest

##########################################################
# 格式化代码
fmt:
	go fmt ./...

# 代码检查
lint:
	golangci-lint run

# 生成API文档
docs:
	swag init

##########################################################
# 测试API
test-api:
	@echo "测试创建任务API..."
	curl -X POST http://localhost:8080/api/v1/tasks  -H "Content-Type: application/json"  -d '{ 
			"name": "CoinGecko Solana七天历史", 
			"description": "CoinGecko Solana七天历史价格",
			"initial_urls": [{ 
				"url": "https://api.coingecko.com/api/v3/coins/solana/market_chart?vs_currency=usd&days=7", 
				"method": "GET" 
			}], 
			"priority": 2, 
			"spider_name": "api_spider", 
			"is_recurring": true, 
			"repeat_interval": 60,
			"max_concurrency": 5, 
			"max_qps": 10, 
			"timeout": 30
			}'
	@echo "\n\n测试获取任务列表API..."
	curl http://localhost:$(APP_PORT)/api/v1/tasks

# 测试待处理URL API
test-pending-urls:
	@echo "测试获取待处理URL API..."
	curl http://localhost:$(APP_PORT)/api/v1/urls/pending?limit=5

# 测试URL队列功能
test-url-queue:
	@echo "测试URL队列功能..."
	./scripts/test-url-queue.sh

# 测试任务执行器服务
test-task-executor:
	@echo "测试任务执行器服务是否正常运行..."
	@echo "检查任务执行器服务端口8082是否在监听..."
	lsof -i :8082 || (echo "错误: 任务执行器服务未在端口8082上监听" && exit 1)
	@echo "检查任务执行器服务是否已注册Dapr Pubsub订阅..."
	curl -s http://localhost:3502/v1.0/metadata | grep -q "url-download" && echo "✅ 任务执行器服务已正确订阅url-download主题" || echo "❌ 任务执行器服务未订阅url-download主题"

# 测试Spider服务
test-spider:
	@echo "测试Spider服务是否正常运行..."
	@echo "检查Spider服务端口8083是否在监听..."
	lsof -i :8083 || (echo "错误: Spider服务未在端口8083上监听" && exit 1)
	@echo "测试crawl接口..."
	curl -X POST http://localhost:3503/v1.0/invoke/spider/method/crawl \
		-H "Content-Type: application/json" \
		-d '{ \
			"id": 1, \
			"task_id": "test-task", \
			"url": "https://www.example.com", \
			"method": "GET", \
			"priority": 2, \
			"status": "pending" \
		}'

# 测试爬虫管理服务API
test-spider-manager:
	@echo "测试爬虫管理服务API..."
	./scripts/test-spider-manager.sh

# 修复任务执行器服务问题
fix-task-executor:
	@echo "正在诊断和修复任务执行器服务问题..."
	./scripts/fix-task-executor.sh

##########################################################

# 帮助信息
help:
	@echo "可用的命令:"
	@echo "  build-all          - 构建所有服务"
	@echo "  build-task-manager - 构建任务管理服务"
	@echo "  build-task-scheduler - 构建任务调度服务"
	@echo "  build-task-executor - 构建任务执行服务"
	@echo "  build-spider       - 构建Spider服务"
	@echo "  build-spider-manager - 构建Spider管理服务"
	@echo "  deps               - 安装go依赖"
	@echo "  test               - 运行测试"
	@echo "  clean              - 清理构建文件"

	@echo "  dapr-run-cmd       - 运行任务管理服务（命令方式）"
	@echo "  dapr-run-config    - 运行任务管理服务（配置方式）"
	@echo "  dapr-stop          - 停止任务管理服务"
	@echo "  dapr-run-dev       - 运行整个系统（开发环境）"
	@echo "  dapr-run-prod      - 运行整个系统（生产环境）"
	@echo "  dapr-run-with-logging - 运行整个系统（带日志收集）"
	@echo "  dapr-run-spider    - 单独运行Spider服务"
	@echo "  dapr-stop-all      - 停止整个系统所有Dapr应用"

	@echo "  redis-start        - 启动Redis容器"
	@echo "  redis-stop         - 停止Redis容器"
	@echo "  mongodb-start      - 启动MongoDB容器"
	@echo "  mongodb-stop       - 停止MongoDB容器"
	@echo "  setup-mongo-config - 设置MongoDB配置到Redis"
	@echo "  mysql-start        - 启动MySQL容器"
	@echo "  mysql-stop         - 停止MySQL容器"
	@echo "  mysql-restart      - 重启MySQL容器"
	@echo "  mysql-logs         - 查看MySQL日志"
	@echo "  mysql-connect      - 连接到MySQL"

	@echo "  switch-to-redis    - 切换到Redis状态存储"
	@echo "  switch-to-mysql    - 切换到MySQL状态存储"
	@echo "  dev-setup-redis    - 设置开发环境（Redis）"
	@echo "  dev-setup-mysql    - 设置开发环境（MySQL）"

	@echo "  docker-build       - 构建Docker镜像"
	@echo "  docker-run         - 运行Docker容器"
	@echo "  docker-build-spider - 构建Spider Docker镜像"
	@echo "  docker-run-spider  - 运行Spider Docker容器"
	@echo "  fmt                - 格式化代码"
	@echo "  lint               - 代码检查"
	@echo "  docs               - 生成API文档"
	
	@echo "  test-api           - 测试API接口"
	@echo "  test-url-queue     - 测试URL队列功能"
	@echo "  test-pending-urls  - 测试待处理URL API"
	@echo "  test-task-executor - 测试任务执行器服务"
	@echo "  test-spider        - 测试Spider服务"
	@echo "  test-spider-manager - 测试爬虫管理服务API"
	@echo "  fix-task-executor  - 诊断和修复任务执行器服务问题"

	@echo "  monitoring-start   - 启动监控系统（Prometheus + Grafana）"
	@echo "  monitoring-stop    - 停止监控系统"
	@echo "  dapr-run-with-monitoring - 使用带监控配置运行Dapr应用"
	@echo "  prometheus-logs    - 查看Prometheus日志"
	@echo "  grafana-logs       - 查看Grafana日志"
	@echo "  logging-start      - 启动日志系统"
	@echo "  loki-logs          - 查看Loki日志"
	@echo "  promtail-logs      - 查看Promtail日志"
	@echo "  help               - 显示帮助信息"
