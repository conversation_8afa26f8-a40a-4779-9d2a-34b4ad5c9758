package logger

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/buffer"
	"go.uber.org/zap/zapcore"
)

var (
	// Logger 全局日志实例
	Logger *zap.Logger
	// Sugar 全局Sugar日志实例，提供更简洁的API
	Sugar *zap.SugaredLogger
)

// compactEncoder 紧凑格式编码器
type compactEncoder struct {
	jsonEncoder zapcore.Encoder
}

// EncodeEntry 实现zapcore.Encoder接口
func (e *compactEncoder) EncodeEntry(entry zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	buf := buffer.NewPool().Get()

	// 格式: [2025-09-10 21:48:20][INFO][filename:line] message {"field": "value"}
	buf.AppendString(fmt.Sprintf("[%s][%s][%s:%d] %s",
		entry.Time.Format("2006-01-02 15:04:05"),
		strings.ToUpper(entry.Level.String()),
		filepath.Base(entry.Caller.File),
		entry.Caller.Line,
		entry.Message))

	// 如果有字段，添加JSON格式的字段
	if len(fields) > 0 {
		buf.AppendString(" ")
		fieldMap := make(map[string]interface{})
		for _, field := range fields {
			switch field.Type {
			case zapcore.StringType:
				fieldMap[field.Key] = field.String
			case zapcore.Int64Type:
				fieldMap[field.Key] = field.Integer
			case zapcore.BoolType:
				fieldMap[field.Key] = field.Integer == 1
			case zapcore.ErrorType:
				if field.Interface != nil {
					fieldMap[field.Key] = field.Interface.(error).Error()
				}
			default:
				fieldMap[field.Key] = field.Interface
			}
		}

		jsonBytes, _ := json.Marshal(fieldMap)
		buf.AppendString(string(jsonBytes))
	}

	buf.AppendString("\n")
	return buf, nil
}

// Clone 实现zapcore.Encoder接口
func (e *compactEncoder) Clone() zapcore.Encoder {
	return &compactEncoder{
		jsonEncoder: e.jsonEncoder.Clone(),
	}
}

// AddArray 实现zapcore.Encoder接口
func (e *compactEncoder) AddArray(key string, marshaler zapcore.ArrayMarshaler) error {
	return e.jsonEncoder.AddArray(key, marshaler)
}

// AddObject 实现zapcore.Encoder接口
func (e *compactEncoder) AddObject(key string, marshaler zapcore.ObjectMarshaler) error {
	return e.jsonEncoder.AddObject(key, marshaler)
}

// AddBinary 实现zapcore.Encoder接口
func (e *compactEncoder) AddBinary(key string, value []byte) {
	e.jsonEncoder.AddBinary(key, value)
}

// AddByteString 实现zapcore.Encoder接口
func (e *compactEncoder) AddByteString(key string, value []byte) {
	e.jsonEncoder.AddByteString(key, value)
}

// AddBool 实现zapcore.Encoder接口
func (e *compactEncoder) AddBool(key string, value bool) {
	e.jsonEncoder.AddBool(key, value)
}

// AddComplex128 实现zapcore.Encoder接口
func (e *compactEncoder) AddComplex128(key string, value complex128) {
	e.jsonEncoder.AddComplex128(key, value)
}

// AddComplex64 实现zapcore.Encoder接口
func (e *compactEncoder) AddComplex64(key string, value complex64) {
	e.jsonEncoder.AddComplex64(key, value)
}

// AddDuration 实现zapcore.Encoder接口
func (e *compactEncoder) AddDuration(key string, value time.Duration) {
	e.jsonEncoder.AddDuration(key, value)
}

// AddFloat64 实现zapcore.Encoder接口
func (e *compactEncoder) AddFloat64(key string, value float64) {
	e.jsonEncoder.AddFloat64(key, value)
}

// AddFloat32 实现zapcore.Encoder接口
func (e *compactEncoder) AddFloat32(key string, value float32) {
	e.jsonEncoder.AddFloat32(key, value)
}

// AddInt 实现zapcore.Encoder接口
func (e *compactEncoder) AddInt(key string, value int) {
	e.jsonEncoder.AddInt(key, value)
}

// AddInt64 实现zapcore.Encoder接口
func (e *compactEncoder) AddInt64(key string, value int64) {
	e.jsonEncoder.AddInt64(key, value)
}

// AddInt32 实现zapcore.Encoder接口
func (e *compactEncoder) AddInt32(key string, value int32) {
	e.jsonEncoder.AddInt32(key, value)
}

// AddInt16 实现zapcore.Encoder接口
func (e *compactEncoder) AddInt16(key string, value int16) {
	e.jsonEncoder.AddInt16(key, value)
}

// AddInt8 实现zapcore.Encoder接口
func (e *compactEncoder) AddInt8(key string, value int8) {
	e.jsonEncoder.AddInt8(key, value)
}

// AddString 实现zapcore.Encoder接口
func (e *compactEncoder) AddString(key string, value string) {
	e.jsonEncoder.AddString(key, value)
}

// AddTime 实现zapcore.Encoder接口
func (e *compactEncoder) AddTime(key string, value time.Time) {
	e.jsonEncoder.AddTime(key, value)
}

// AddUint 实现zapcore.Encoder接口
func (e *compactEncoder) AddUint(key string, value uint) {
	e.jsonEncoder.AddUint(key, value)
}

// AddUint64 实现zapcore.Encoder接口
func (e *compactEncoder) AddUint64(key string, value uint64) {
	e.jsonEncoder.AddUint64(key, value)
}

// AddUint32 实现zapcore.Encoder接口
func (e *compactEncoder) AddUint32(key string, value uint32) {
	e.jsonEncoder.AddUint32(key, value)
}

// AddUint16 实现zapcore.Encoder接口
func (e *compactEncoder) AddUint16(key string, value uint16) {
	e.jsonEncoder.AddUint16(key, value)
}

// AddUint8 实现zapcore.Encoder接口
func (e *compactEncoder) AddUint8(key string, value uint8) {
	e.jsonEncoder.AddUint8(key, value)
}

// AddUintptr 实现zapcore.Encoder接口
func (e *compactEncoder) AddUintptr(key string, value uintptr) {
	e.jsonEncoder.AddUintptr(key, value)
}

// AddReflected 实现zapcore.Encoder接口
func (e *compactEncoder) AddReflected(key string, value interface{}) error {
	return e.jsonEncoder.AddReflected(key, value)
}

// OpenNamespace 实现zapcore.Encoder接口
func (e *compactEncoder) OpenNamespace(key string) {
	e.jsonEncoder.OpenNamespace(key)
}

// Init 初始化日志系统
func Init() error {
	// 创建自定义编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    customLevelEncoder,
		EncodeTime:     customTimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   customCallerEncoder,
	}

	// 创建JSON编码器，然后包装成自定义格式
	encoder := &compactEncoder{
		jsonEncoder: zapcore.NewJSONEncoder(encoderConfig),
	}

	// 设置日志级别
	level := getLogLevel()

	// 创建核心
	core := zapcore.NewCore(
		encoder,
		zapcore.AddSync(os.Stdout),
		level,
	)

	// 创建logger，添加调用者信息
	Logger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	Sugar = Logger.Sugar()

	return nil
}

// customTimeEncoder 自定义时间编码器，格式：[2025-09-10 10:08:59]
func customTimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(fmt.Sprintf("[%s]", t.Format("2006-01-02 15:04:05")))
}

// customLevelEncoder 自定义级别编码器，格式：[INFO]
func customLevelEncoder(level zapcore.Level, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(fmt.Sprintf("[%s]", strings.ToUpper(level.String())))
}

// customCallerEncoder 自定义调用者编码器，格式：[filename:line]
func customCallerEncoder(caller zapcore.EntryCaller, enc zapcore.PrimitiveArrayEncoder) {
	// 获取文件名（不包含路径）
	filename := filepath.Base(caller.File)
	enc.AppendString(fmt.Sprintf("[%s:%d]", filename, caller.Line))
}

// getLogLevel 从环境变量获取日志级别
func getLogLevel() zapcore.Level {
	levelStr := strings.ToLower(os.Getenv("LOG_LEVEL"))
	switch levelStr {
	case "debug":
		return zapcore.DebugLevel
	case "info":
		return zapcore.InfoLevel
	case "warn", "warning":
		return zapcore.WarnLevel
	case "error":
		return zapcore.ErrorLevel
	case "fatal":
		return zapcore.FatalLevel
	default:
		return zapcore.InfoLevel
	}
}

// Info 记录Info级别日志
func Info(msg string, fields ...zap.Field) {
	Logger.Info(msg, fields...)
}

// Debug 记录Debug级别日志
func Debug(msg string, fields ...zap.Field) {
	Logger.Debug(msg, fields...)
}

// Warn 记录Warn级别日志
func Warn(msg string, fields ...zap.Field) {
	Logger.Warn(msg, fields...)
}

// Error 记录Error级别日志
func Error(msg string, fields ...zap.Field) {
	Logger.Error(msg, fields...)
}

// Fatal 记录Fatal级别日志并退出程序
func Fatal(msg string, fields ...zap.Field) {
	Logger.Fatal(msg, fields...)
}

// Infof 记录Info级别日志（格式化）
func Infof(template string, args ...interface{}) {
	Sugar.Infof(template, args...)
}

// Debugf 记录Debug级别日志（格式化）
func Debugf(template string, args ...interface{}) {
	Sugar.Debugf(template, args...)
}

// Warnf 记录Warn级别日志（格式化）
func Warnf(template string, args ...interface{}) {
	Sugar.Warnf(template, args...)
}

// Errorf 记录Error级别日志（格式化）
func Errorf(template string, args ...interface{}) {
	Sugar.Errorf(template, args...)
}

// Fatalf 记录Fatal级别日志（格式化）并退出程序
func Fatalf(template string, args ...interface{}) {
	Sugar.Fatalf(template, args...)
}

// WithFields 创建带字段的日志记录器
func WithFields(fields ...zap.Field) *zap.Logger {
	return Logger.With(fields...)
}

// Sync 同步日志缓冲区
func Sync() error {
	if Logger != nil {
		return Logger.Sync()
	}
	return nil
}

// GetCaller 获取调用者信息，用于手动添加调用者信息
func GetCaller(skip int) (string, int) {
	_, file, line, ok := runtime.Caller(skip + 1)
	if !ok {
		return "unknown", 0
	}
	return filepath.Base(file), line
}
