from typing import List, Any, Optional
import database
from datetime import datetime


class KLineService:
    # K线间隔到表名的映射
    INTERVAL_TABLE_MAP = {
        "1d": "binance_kline_daily_dwd",
        "1h": "binance_kline_hour_dwd",
        "1w": "binance_kline_week_dwd",
        "1M": "binance_kline_month_dwd"
    }

    @staticmethod
    async def query_kline_data(
        symbol: str,
        interval: str,
        start_time: Optional[int] = None,
        end_time: Optional[int] = None,
        limit: int = 500
    ) -> List[List[Any]]:
        """
        查询K线数据

        Args:
            symbol (str): 交易对符号
            interval (str): K线间隔 (1d, 1h, 1w, 1M)
            start_time (int, optional): 开始时间戳(秒)
            end_time (int, optional): 结束时间戳(秒)
            limit (int): 限制条数，默认500

        Returns:
            List[List[Any]]: K线数据列表
        """
        # 验证interval参数
        if interval not in KLineService.INTERVAL_TABLE_MAP:
            raise ValueError(f"Unsupported interval: {interval}")

        table_name = KLineService.INTERVAL_TABLE_MAP[interval]

        # 构建SQL查询
        query_parts = [
            f"SELECT open_time, open_price, high_price, low_price, close_price, volume,",
            f"       close_time, quote_volume, trade_count, taker_buy_volume, taker_buy_quote_volume",
            f"FROM {table_name}",
            f"WHERE symbol = %s"
        ]
        params = [symbol]

        # 添加时间范围条件
        if start_time:
            query_parts.append("  AND open_time >= FROM_UNIXTIME(%s)")
            params.append(start_time)

        if end_time:
            query_parts.append("  AND open_time <= FROM_UNIXTIME(%s)")
            params.append(end_time)

        # 按开盘时间排序
        query_parts.append("ORDER BY open_time ASC")

        # 添加限制
        query_parts.append("LIMIT %s")
        params.append(limit)

        # 组合查询语句
        query = "\n".join(query_parts)

        # 执行查询
        params = tuple(params)
        db_service = await database.DatabaseService.get_instance()
        result = await db_service.execute_query(query, params)

        # 格式化返回结果
        formatted_result = []
        for row in result:
            # 处理时间戳，确保返回秒级时间戳
            open_time = row['open_time']
            close_time = row['close_time']
            
            # 转换为秒级时间戳
            if isinstance(open_time, datetime):
                open_time_s = int(open_time.timestamp())
            else:
                open_time_s = int(open_time)
                
            if isinstance(close_time, datetime):
                close_time_s = int(close_time.timestamp())
            else:
                close_time_s = int(close_time)

            formatted_row = [
                open_time_s,
                str(row['open_price']),
                str(row['high_price']),
                str(row['low_price']),
                str(row['close_price']),
                str(row['volume']),
                close_time_s,
                str(row['quote_volume']),
                row['trade_count'],
                str(row['taker_buy_volume']),
                str(row['taker_buy_quote_volume'])
            ]
            formatted_result.append(formatted_row)

        return formatted_result