import os
import logging
import asyncmy
from dapr.clients import DaprClient

logger = logging.getLogger()

class DatabaseService:
    _instance = None
    _pool = None

    @classmethod
    async def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
            await cls._instance._initialize_pool()
        return cls._instance

    async def _initialize_pool(self):
        # 从Dapr Secret Store获取数据库配置
        try:
            with DaprClient() as client:
                secrets_bulk = client.get_bulk_secret(store_name="secret-store")
                secrets = secrets_bulk.secrets
            
                # 数据结构示例: {'MYSQL_HOST': {'MYSQL_HOST': '*********'}}
                mysql_host = secrets.get("MYSQL_HOST", {}).get("MYSQL_HOST", os.getenv('MYSQL_HOST', 'localhost'))
                mysql_port = int(secrets.get("MYSQL_PORT", {}).get("MYSQL_PORT", os.getenv('MYSQL_PORT', '3306')))
                mysql_user = secrets.get("MYSQL_USER", {}).get("MYSQL_USER", os.getenv('MYSQL_USER', 'root'))
                mysql_password = secrets.get("MYSQL_PASSWORD", {}).get("MYSQL_PASSWORD", os.getenv('MYSQL_PASSWORD', '123456')) 
                mysql_database = secrets.get("MYSQL_DATABASE", {}).get("MYSQL_DATABASE", os.getenv('MYSQL_DATABASE', 'testdb'))
                
                # 打印获取到的配置信息
                logger.info(f"Database config - Host: {mysql_host}, Port: {mysql_port}, User: {mysql_user}, Database: {mysql_database}")
        except Exception as e:
            # 如果无法从secret store获取，则使用环境变量作为后备
            logger.error(f"Failed to get secrets from Dapr Secret Store: {e}, using environment variables as fallback")
   
            mysql_host = os.getenv('MYSQL_HOST', 'localhost')
            mysql_port = int(os.getenv('MYSQL_PORT', '3306'))
            mysql_user = os.getenv('MYSQL_USER', 'root')
            mysql_password = os.getenv('MYSQL_PASSWORD', '123456')
            mysql_database = os.getenv('MYSQL_DATABASE', 'testdb')
            
            # 打印后备配置信息
            logger.info(f"Fallback database config - Host: {mysql_host}, Port: {mysql_port}, User: {mysql_user}, Database: {mysql_database}")

        try:
            self._pool = await asyncmy.create_pool(
                host=mysql_host,
                port=mysql_port,
                user=mysql_user,
                password=mysql_password,
                db=mysql_database,
                autocommit=True
            )
            # 确保连接池已完全初始化
            if self._pool:
                async with self._pool.acquire() as conn:
                    await conn.ping()
        except Exception as e:
            self._pool = None
            raise RuntimeError(f"Failed to initialize database pool: {e}")

    async def execute_query(self, query, params=None):
        #输出完整sql语句,query为模版，params为参数
        print(f"Executing SQL query: {query}, Params: {params}")
        if self._pool is None:
            await self._initialize_pool()

        try:
            if self._pool is None:
                raise RuntimeError("Database pool is not initialized")
                
            async with self._pool.acquire() as conn:
                async with conn.cursor(asyncmy.cursors.DictCursor) as cur:
                    await cur.execute(query, params)
                    result = await cur.fetchall()
                    return result
        except RuntimeError as e:
            if "Event loop is closed" in str(e):
                # 事件循环已关闭，重新初始化连接池
                await self._initialize_pool()
                if self._pool is None:
                    raise RuntimeError("Database pool is not initialized after re-initialization")
                    
                async with self._pool.acquire() as conn:
                    async with conn.cursor(asyncmy.cursors.DictCursor) as cur:
                        await cur.execute(query, params)
                        result = await cur.fetchall()
                        return result
            raise

    async def insert(self, table_name, data):
        """插入数据到指定表中

        Args:
            table_name (str): 表名
            data (dict or list): 要插入的数据，可以是单行(字典)或多行(字典列表)

        Returns:
            int: 插入的记录数
        """
        if self._pool is None:
            await self._initialize_pool()
            
        if self._pool is None:
            raise RuntimeError("Database pool is not initialized")

        try:
            async with self._pool.acquire() as conn:
                # 处理单行插入
                if isinstance(data, dict):
                    columns = ', '.join(data.keys())
                    placeholders = ', '.join(['%s'] * len(data))
                    query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
                    params = tuple(data.values())
                    
                    print(f"Executing SQL insert: {query}, Params: {params}")
                    
                    async with conn.cursor() as cur:
                        await cur.execute(query, params)
                        # 返回最后插入记录的ID和影响的行数
                        return {"id": cur.lastrowid, "count": cur.rowcount}
                
                # 处理多行插入
                elif isinstance(data, list) and data:
                    if not all(isinstance(item, dict) for item in data):
                        raise ValueError("All items in the list must be dictionaries")
                    
                    columns = ', '.join(data[0].keys())
                    placeholders = ', '.join(['%s'] * len(data[0]))
                    multi_placeholders = ', '.join([f'({placeholders})'] * len(data))
                    query = f"INSERT INTO {table_name} ({columns}) VALUES {multi_placeholders}"
                    
                    # 展平参数列表
                    params = []
                    for item in data:
                        params.extend(item.values())
                    params = tuple(params)
                    
                    print(f"Executing SQL batch insert: {query}, Params: {params}")
                    
                    async with conn.cursor() as cur:
                        await cur.execute(query, params)
                        return cur.rowcount
                else:
                    raise ValueError("Data must be a dictionary or a list of dictionaries")
        except RuntimeError as e:
            if "Event loop is closed" in str(e):
                # 事件循环已关闭，重新初始化连接池
                await self._initialize_pool()
                if self._pool is None:
                    raise RuntimeError("Database pool is not initialized after re-initialization")
                return await self.insert(table_name, data)
            raise

    async def update(self, table_name, data, where_clause=None, where_params=None):
        """更新表中的数据

        Args:
            table_name (str): 表名
            data (dict): 要更新的数据（字段名:值）
            where_clause (str, optional): WHERE子句
            where_params (tuple, optional): WHERE子句的参数

        Returns:
            int: 更新的记录数
        """
        if self._pool is None:
            await self._initialize_pool()

        if not data:
            raise ValueError("Data dictionary cannot be empty")

        try:
            async with self._pool.acquire() as conn:
                # 构建SET子句
                set_clause = ', '.join([f"{key} = %s" for key in data.keys()])
                params = list(data.values())
                
                # 构建完整查询
                query = f"UPDATE {table_name} SET {set_clause}"
                if where_clause:
                    query += f" WHERE {where_clause}"
                    if where_params:
                        params.extend(where_params)
                
                params = tuple(params)
                print(f"Executing SQL update: {query}, Params: {params}")
                
                async with conn.cursor() as cur:
                    await cur.execute(query, params)
                    return cur.rowcount
        except RuntimeError as e:
            if "Event loop is closed" in str(e):
                # 事件循环已关闭，重新初始化连接池
                await self._initialize_pool()
                return await self.update(table_name, data, where_clause, where_params)
            raise

    @classmethod
    async def close_pool(cls):
        if cls._instance and cls._instance._pool:
            cls._instance._pool.close()
            await cls._instance._pool.wait_closed()
            cls._instance._pool = None
            cls._instance = None

# 确保在应用启动时初始化数据库连接
async def initialize_database():
    try:
        await DatabaseService.get_instance()
    except Exception as e:  
        logger.error(e)
        logger.error(traceback.format_exc())