package database

import (
	"database/sql"
	"shovel/biz_services/account/internal/logger"
	"time"

	"go.uber.org/zap"
)

// User 用户模型
type User struct {
	ID         int64     `json:"id"`
	Account    string    `json:"account"`
	LoginTime  time.Time `json:"login_time"`
	CreateTime time.Time `json:"create_time"`
}

// GetUserByAccount 根据账户地址获取用户信息
func GetUserByAccount(account string) (*User, error) {
	query := "SELECT id, account, login_time, create_time FROM user WHERE account = ?"
	row := db.QueryRow(query, account)

	user := &User{}
	err := row.Scan(&user.ID, &user.Account, &user.LoginTime, &user.CreateTime)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 用户不存在
		}
		return nil, err
	}

	return user, nil
}

// CreateUser 创建新用户
func CreateUser(account string) (*User, error) {
	query := "INSERT INTO user (account, login_time) VALUES (?, NOW())"
	result, err := db.Exec(query, account)
	if err != nil {
		return nil, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	// 查询创建的用户信息
	user, err := GetUserByID(id)
	if err != nil {
		return nil, err
	}

	logger.Info("创建新用户",
		zap.Int64("id", id),
		zap.String("account", account))
	return user, nil
}

// GetUserByID 根据ID获取用户信息
func GetUserByID(id int64) (*User, error) {
	query := "SELECT id, account, login_time, create_time FROM user WHERE id = ?"
	row := db.QueryRow(query, id)

	user := &User{}
	err := row.Scan(&user.ID, &user.Account, &user.LoginTime, &user.CreateTime)
	if err != nil {
		return nil, err
	}

	return user, nil
}

// UpdateUserLoginTime 更新用户登录时间
func UpdateUserLoginTime(account string) error {
	query := "UPDATE user SET login_time = NOW() WHERE account = ?"
	_, err := db.Exec(query, account)
	if err != nil {
		return err
	}

	logger.Info("更新用户登录时间", zap.String("account", account))
	return nil
}
