package models

// GlobalRateLimiterRequest 全局限流器请求结构
type GlobalRateLimiterRequest struct {
	LimiterID      string  `json:"limiter_id" binding:"required"`     // 全局限流器ID
	ReqID          string  `json:"req_id" binding:"required"`         // 唯一请求ID，由调用方生成
	MaxConcurrency int     `json:"max_concurrency" binding:"min=1"`   // 最大并发数
	MaxQPS         float64 `json:"max_qps,omitempty" binding:"min=0"` // 最大QPS（每秒请求数，支持小数，0表示不限制）
	CallTimeout    int     `json:"call_timeout" binding:"min=1"`      // 调用方处理超时时间（秒）
}

// GlobalRateLimiterResponse 全局限流器响应结构
type GlobalRateLimiterResponse struct {
	Success            bool   `json:"success"`                       // 是否成功
	Message            string `json:"message"`                       // 响应消息
	Allowed            bool   `json:"allowed,omitempty"`             // 是否允许继续（仅在获取资源时有效）
	WaitTime           int    `json:"wait_time,omitempty"`           // 建议等待时间（秒），当Allowed为false时有效
	CurrentTokens      int    `json:"current_tokens,omitempty"`      // 当前可用令牌数（QPS限制）
	CurrentConcurrency int    `json:"current_concurrency,omitempty"` // 当前并发数
	Error              string `json:"error,omitempty"`               // 错误信息
}

// ReleaseRateLimiterRequest 释放限流器资源请求结构
type ReleaseRateLimiterRequest struct {
	LimiterID string `json:"limiter_id" binding:"required"` // 全局限流器ID
	ReqID     string `json:"req_id" binding:"required"`     // 要释放的请求
}
